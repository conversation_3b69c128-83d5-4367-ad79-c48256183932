from io import StringIO
from django.test import TestCase
from django.core.management import call_command
from vantedge.users.models import Company
from vantedge.iot.models import EventType


class LoadEventTypesCommandTest(TestCase):
    def setUp(self):
        self.company = Company.objects.create(name="Test Company")

    def test_load_event_types_global(self):
        """Test loading global event types."""
        out = StringIO()
        call_command('load_event_types', stdout=out)

        # Check that event types were created
        self.assertTrue(EventType.objects.filter(name="Truck", company=None).exists())
        self.assertTrue(EventType.objects.filter(name="Transfer", company=None).exists())

        # Check parent-child relationship
        truck = EventType.objects.get(name="Truck", company=None)
        transfer = EventType.objects.get(name="Transfer", company=None)
        self.assertEqual(transfer.parent, truck)

        # Check output
        output = out.getvalue()
        self.assertIn("Created Truck event type", output)
        self.assertIn("Created Transfer event type", output)
        self.assertIn("Successfully loaded event types", output)

    def test_load_event_types_company(self):
        """Test loading event types for a specific company."""
        out = StringIO()
        call_command('load_event_types', company="Test Company", stdout=out)

        # Check that event types were created
        self.assertTrue(EventType.objects.filter(name="Truck", company=self.company).exists())
        self.assertTrue(EventType.objects.filter(name="Transfer", company=self.company).exists())

        # Check parent-child relationship
        truck = EventType.objects.get(name="Truck", company=self.company)
        transfer = EventType.objects.get(name="Transfer", company=self.company)
        self.assertEqual(transfer.parent, truck)

        # Check output
        output = out.getvalue()
        self.assertIn("Using company: Test Company", output)
        self.assertIn("Created Truck event type", output)
        self.assertIn("Created Transfer event type", output)
        self.assertIn("Successfully loaded event types", output)

    def test_load_event_types_force(self):
        """Test force option when event types already exist."""
        # Create event types first
        call_command('load_event_types')

        # Try to create again without force
        out = StringIO()
        call_command('load_event_types', stdout=out)
        output = out.getvalue()
        self.assertIn("Truck event type already exists", output)

        # Try to create again with force
        out = StringIO()
        call_command('load_event_types', force=True, stdout=out)
        output = out.getvalue()
        self.assertIn("Deleting existing event types", output)
        self.assertIn("Created Truck event type", output)
        self.assertIn("Created Transfer event type", output)
        self.assertIn("Successfully loaded event types", output)
