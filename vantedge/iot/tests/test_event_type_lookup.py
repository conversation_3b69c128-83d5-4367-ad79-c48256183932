from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from django.contrib.auth import get_user_model

from vantedge.users.models import Company
from vantedge.iot.models import EventType

User = get_user_model()


class EventTypeLookupTestCase(TestCase):
    def setUp(self):
        # Create a test user and company
        self.company = Company.objects.create(name="Test Company")
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword",
            company=self.company,
        )

        # Create a token for the user
        self.token = Token.objects.create(user=self.user)

        # Create a test event type with a code
        self.event_type = EventType.objects.create(
            name="Test Event Type",
            description="A test event type",
            company=self.company,
            code="TEST123",
            is_active=True,
            max_events_per_location=100,
            retention_days=30,
        )

        # Create a global event type with a code
        self.global_event_type = EventType.objects.create(
            name="Global Event Type",
            description="A global event type",
            company=None,
            code="GLOBAL456",
            is_active=True,
            max_events_per_location=100,
            retention_days=30,
        )

        # Set up the client
        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

    def test_retrieve_event_type_by_id(self):
        """Test retrieving an event type by its ID."""
        url = reverse('iot-event-types-detail', args=[self.event_type.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], self.event_type.name)
        self.assertEqual(response.data['code'], 'TEST123')

    def test_retrieve_event_type_by_code(self):
        """Test retrieving an event type by its code."""
        url = reverse('iot-event-types-detail', args=['TEST123'])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], self.event_type.name)
        self.assertEqual(response.data['id'], str(self.event_type.id))

    def test_retrieve_global_event_type_by_code(self):
        """Test retrieving a global event type by its code."""
        url = reverse('iot-event-types-detail', args=['GLOBAL456'])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], self.global_event_type.name)
        self.assertEqual(response.data['id'], str(self.global_event_type.id))

    def test_retrieve_nonexistent_event_type(self):
        """Test retrieving a non-existent event type."""
        url = reverse('iot-event-types-detail', args=['NONEXISTENT'])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
