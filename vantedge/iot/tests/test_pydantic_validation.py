import uuid
from django.test import TestCase
from django.core.exceptions import ValidationError
from vantedge.users.models import Company
from vantedge.iot.models.base import Event, EventType
from vantedge.iot.models.proxy import TruckEvent, TransferEvent
from vantedge.iot.models.schemas import TruckEventData, TransferEventData


class PydanticValidationTestCase(TestCase):
    def setUp(self):
        # Create a test company
        self.company = Company.objects.create(name="Test Company")

        # Create event types
        self.truck_event_type = EventType.objects.create(
            name="Truck",
            description="Truck-related events",
            company=self.company
        )

        self.transfer_event_type = EventType.objects.create(
            name="Transfer",
            description="Transfer-related events",
            company=self.company
        )

    def test_truck_event_valid_data(self):
        """Test that a TruckEvent with valid data passes validation."""
        event = TruckEvent(
            company=self.company,
            event_type=self.truck_event_type,
            data={
                "truck_id": "TRUCK123",
                "event": "arrival"
            }
        )

        # This should not raise any exceptions
        event.full_clean()
        event.save()

        # Verify the data was saved correctly
        saved_event = TruckEvent.objects.get(id=event.id)
        self.assertEqual(saved_event.data["truck_id"], "TRUCK123")
        self.assertEqual(saved_event.data["event"], "arrival")

    def test_truck_event_invalid_data(self):
        """Test that a TruckEvent with invalid data fails validation."""
        event = TruckEvent(
            company=self.company,
            event_type=self.truck_event_type,
            data={
                # Missing truck_id
                "event": "arrival"
            }
        )

        # This should raise a ValidationError
        with self.assertRaises(ValidationError):
            event.full_clean()

    def test_truck_event_invalid_event_type(self):
        """Test that a TruckEvent with an invalid event type fails validation."""
        event = TruckEvent(
            company=self.company,
            event_type=self.truck_event_type,
            data={
                "truck_id": "TRUCK123",
                "event": "invalid_event"  # Not in ["arrival", "departure"]
            }
        )

        # This should raise a ValidationError
        with self.assertRaises(ValidationError):
            event.full_clean()

    def test_transfer_event_valid_data(self):
        """Test that a TransferEvent with valid data passes validation."""
        event = TransferEvent(
            company=self.company,
            event_type=self.transfer_event_type,
            data={
                "transfer_id": "TRANSFER123",
                "event": "unload_start",
                "truck_id": "TRUCK123"
            }
        )

        # This should not raise any exceptions
        event.full_clean()
        event.save()

        # Verify the data was saved correctly
        saved_event = TransferEvent.objects.get(id=event.id)
        self.assertEqual(saved_event.data["transfer_id"], "TRANSFER123")
        self.assertEqual(saved_event.data["event"], "unload_start")

    def test_transfer_event_unload_complete_missing_times(self):
        """Test that an unload_complete event requires start_time and end_time."""
        event = TransferEvent(
            company=self.company,
            event_type=self.transfer_event_type,
            data={
                "transfer_id": "TRANSFER123",
                "event": "unload_complete",
                "truck_id": "TRUCK123"
                # Missing start_time and end_time
            }
        )

        # This should raise a ValidationError
        with self.assertRaises(ValidationError):
            event.full_clean()

    def test_event_proxy_casting(self):
        """Test that EventProxy correctly casts to the appropriate proxy model."""
        from vantedge.iot.models.base import EventProxy

        # Create an event using EventProxy
        event = EventProxy(
            company=self.company,
            event_type=self.truck_event_type,
            data={
                "truck_id": "TRUCK123",
                "event": "arrival"
            }
        )

        # Verify that the event was cast to TruckEvent
        self.assertIsInstance(event, TruckEvent)

        # Create another event using EventProxy
        event = EventProxy(
            company=self.company,
            event_type=self.transfer_event_type,
            data={
                "transfer_id": "TRANSFER123",
                "event": "unload_start",
                "truck_id": "TRUCK123"
            }
        )

        # Verify that the event was cast to TransferEvent
        self.assertIsInstance(event, TransferEvent)
