import logging
from django.contrib import admin
from django.contrib.admin import widgets
from django.contrib.contenttypes.models import ContentType
from django.db.models import <PERSON>, <PERSON>, <PERSON>r<PERSON>ield, TextField, ManyToManyField, Foreign<PERSON>ey, DateField
from django.db.models.functions import Concat

from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.serializers import ValidationError
from rest_framework.throttling import SimpleRateThrottle


from functools import wraps
from pydantic.error_wrappers import ValidationError as PydanticValidationError


logger = logging.getLogger(__name__)

class EasyAuditModelConfigMixin:
    easyaudit_crud_log = True # This takes precedence over other configs

    easyaudit_crud_retention = 0 # always
    easyaudit_request_retention = 0 # always


class EasyAuditViewsetConfigMixin():
    easyaudit_request_log = True # This takes precedence over other configs

    def initial(self, request, *args, **kwargs):
        request.META["easyaudit_request_log"] = self.easyaudit_request_log
        return super().initial(request, *args, **kwargs)


class ViewsetRateThrottle(SimpleRateThrottle):
    cache_format = 'throttle_%(scope)s_%(ident)s_%(viewset)s_%(action)s'
    scope = 'user'

    def get_cache_key(self, request, view):
        if request.user and request.user.is_authenticated:
            ident = request.user.pk
        else:
            ident = self.get_ident(request)

        key = self.cache_format % {
            'scope': self.scope,
            'ident': ident,
            'viewset': view.__class__.__name__,
            'action': view.action
        }

        return key

class BaseViewsetThrottleMixin():
    throttle_classes = [ViewsetRateThrottle]

    throttle_rates = {
        "list": "20/minute",      # Limit 20 requests per minute for listing
        "retrieve": "20/minute",   # Limit 20 requests per minute for retrieving details
        "create": "10/minute",     # Limit 10 requests per minute for creating objects
        "update": "10/minute",     # Limit 10 requests per minute for updating
        "partial_update": "10/minute", # Limit 10 requests per minute for partial update objects
        "destroy": "10/minute",    # Limit 10 request per minute for deleting
        "count": "20/minute",  # Limit based on action name
        "autocomplete": "30/minute" # Limit based on action name
    }

    def get_throttles(self):
        """
        Overrides DRF's `get_throttles` to dynamically set throttle rates per action.
        """
        action_throttle_rate = getattr(self, "throttle_rates", {}).get(self.action)

        if action_throttle_rate:
            return [
                type(
                    f"Dynamic{throttle_class.__name__}",
                    (throttle_class,),
                    {"rate": action_throttle_rate}
                )()
                for throttle_class in self.throttle_classes
            ]

        return [throttle() for throttle in self.throttle_classes]


class AppPermissionsMixin(admin.ModelAdmin):
    def formfield_for_manytomany(self, db_field, request=None, **kwargs):
        """
        Exclude the permissions of the model from available_permissions
        """
        formfield_m2m = super().formfield_for_manytomany(db_field, request, **kwargs)
        if db_field.name == "available_permissions":
            content_type = ContentType.objects.get_for_model(self.model)
            formfield_m2m.widget = widgets.FilteredSelectMultiple(
                db_field.verbose_name, db_field.name in self.filter_vertical
            )
        return formfield_m2m

class MetadataViewSetMixin():
    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def metadata(self, request, *args, **kwargs):
        content_type = ContentType.objects.get_for_model(self.get_serializer_class().Meta.model)
        return Response({
            "app_label": content_type.app_label,
            "model": content_type.model
        })

def validate_schemafield(field_names: list):
    def decorator(serializer_class):
        original_validator = serializer_class.validate

        @wraps(original_validator)
        def wrapped_validate(self, attrs):
            model_class = getattr(self.Meta, "model") if self.Meta else None
            assert model_class != None, "model class not in Meta"

            if model_class._meta.proxy_for_model:
                model_class = model_class._meta.proxy_for_model

            for field_name in field_names:
                if field_name in attrs:
                    field = model_class._meta.get_field("data")
                    kwargs_schema = field.deconstruct()[3].get("schema")

                    if type(kwargs_schema) is tuple:
                        schema = kwargs_schema[0]
                    # probably this is vant_util schema mixin
                    elif kwargs_schema is dict:
                        if hasattr(model_class, "data_schema"):
                            schema = getattr(model_class, "data_schema")
                            if not schema:
                                raise Exception("Proxied model has no schema")
                    else:
                        raise Exception("Schema not found")

                    try:
                        schema(**attrs[field_name])
                    except PydanticValidationError as e:
                        raise ValidationError(f"Validation schema failed, {e}")

            # Call the original `validate` method
            return original_validator(self, attrs)

        # Override the `validate` method in the serializer class
        serializer_class.validate = wrapped_validate
        return serializer_class

    return decorator


class SearchFilterMixin():
    """
    SearchFilterMixin
    A mixin that adds generic search functionality to Django FilterSets.
    It allows for searching across all fields defined in the Meta class of the FilterSet.
    The search query is applied to both regular model fields (e.g., CharField, TextField)
    and related fields (e.g., ForeignKey) using double underscore notation for lookups.
    Case-insensitive filtering is performed for fields and related model fields.
    """

    def search_filter(self, qs, fn, value):
        value = value.strip().lower()
        filter_conditions = Q()

        if "mark" in self.Meta.fields and "number" in self.Meta.fields:
            qs = qs.annotate(car_name=Concat(F("mark"), F("number")))
            filter_conditions |= Q(**{"car_name__icontains": value})
        # Loop through all fields in Meta.fields
        for field_name in self.Meta.fields:
            filter_conditions |= self._handle_field(field_name, value)

        return qs.filter(filter_conditions)

    def _handle_field(self, field_name, value):
        """
        Handle fields (CharField, Choices, TextField, ForeignKey).
        """
        filter_conditions = Q()
        related_sub_field = None
        if "__" in field_name:
            field_name, related_sub_field = field_name.split("__", 1)
        field = self.Meta.model._meta.get_field(field_name)

        try:
            if isinstance(field , (ForeignKey, ManyToManyField)) and related_sub_field:
                filter_conditions |= Q(**{f"{field_name}__{related_sub_field}__icontains": value})
            elif isinstance(field, CharField) and hasattr(field, 'choices') and field.choices:
                for choice, label in field.choices:
                    if value in label.lower():
                        filter_conditions |= Q(**{f"{field_name}__icontains": choice})
            elif isinstance(field, (CharField, TextField, DateField)):
                filter_conditions |= Q(**{f"{field_name}__icontains": value})
        except Exception as e:
            logger.warning('EXCEPTION: %s', e)

        return filter_conditions


class IDOrCodeMixin:
    """
    Mixin to allow retrieving objects by either ID (UUID) or code.

    This mixin provides a get_object method that attempts to find an object
    by its ID first, and if that fails, by its code.
    """

    def get_object(self):
        """
        Retrieve the object by either ID or code.
        """
        # Get the lookup value from the URL
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        lookup_value = self.kwargs[lookup_url_kwarg]

        # Get the filtered queryset
        queryset = self.filter_queryset(self.get_queryset())

        # Try to find the object by ID first (if it's a valid UUID)
        try:
            uuid_obj = uuid.UUID(lookup_value)
            obj = queryset.filter(id=uuid_obj).first()
            if obj:
                # May raise a permission denied
                self.check_object_permissions(self.request, obj)
                return obj
        except (ValueError, TypeError):
            # Not a valid UUID, continue to try by code
            pass

        # Try to find the object by code
        obj = queryset.filter(code=lookup_value).first()
        if obj:
            # May raise a permission denied
            self.check_object_permissions(self.request, obj)
            return obj

        # If we get here, the object wasn't found
        model_name = queryset.model.__name__
        raise Http404(f"No {model_name} found with ID or code '{lookup_value}'")
